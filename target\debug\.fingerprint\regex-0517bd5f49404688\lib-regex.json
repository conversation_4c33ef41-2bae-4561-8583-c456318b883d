{"rustc": 3926191382657067107, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 7196364801131986759, "deps": [[555019317135488525, "regex_automata", false, 5703831787624791556], [2779309023524819297, "aho_corasick", false, 3145316691238717801], [9408802513701742484, "regex_syntax", false, 7219131114552852066], [15932120279885307830, "memchr", false, 2735511109988467674]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-0517bd5f49404688\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}