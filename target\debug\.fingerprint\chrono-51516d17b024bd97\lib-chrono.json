{"rustc": 3926191382657067107, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 2241668132362809309, "path": 17851134008603083449, "deps": [[5157631553186200874, "num_traits", false, 13063769646801448057], [9689903380558560274, "serde", false, 6511889393101391260], [11505586985402185701, "windows_link", false, 13887057546528309130]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-51516d17b024bd97\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}