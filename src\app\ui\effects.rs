/// 炫酷视觉特效系统
/// 
/// 提供渐变背景、霓虹发光、玻璃拟态等现代视觉效果

use eframe::egui::{self, Color32, Painter, Pos2, Rect, Rounding, Stroke, Vec2};
use crate::app::ui::theme::ColorPalette;

/// 渐变方向
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy)]
pub enum GradientDirection {
    Horizontal,
    Vertical,
    Diagonal,
    Radial,
}

/// 渐变背景绘制器
pub struct GradientBackground {
    colors: Vec<Color32>,
    direction: GradientDirection,
    opacity: f32,
}

impl GradientBackground {
    pub fn new(colors: Vec<Color32>, direction: GradientDirection) -> Self {
        Self {
            colors,
            direction,
            opacity: 1.0,
        }
    }
    
    pub fn with_opacity(mut self, opacity: f32) -> Self {
        self.opacity = opacity.clamp(0.0, 1.0);
        self
    }
    
    /// 绘制渐变背景
    pub fn draw(&self, painter: &Painter, rect: Rect) {
        if self.colors.len() < 2 {
            return;
        }
        
        match self.direction {
            GradientDirection::Horizontal => self.draw_horizontal_gradient(painter, rect),
            GradientDirection::Vertical => self.draw_vertical_gradient(painter, rect),
            GradientDirection::Diagonal => self.draw_diagonal_gradient(painter, rect),
            GradientDirection::Radial => self.draw_radial_gradient(painter, rect),
        }
    }
    
    fn draw_horizontal_gradient(&self, painter: &Painter, rect: Rect) {
        let steps = 50;
        let step_width = rect.width() / steps as f32;
        
        for i in 0..steps {
            let progress = i as f32 / (steps - 1) as f32;
            let color = self.interpolate_color(progress);
            
            let x = rect.min.x + i as f32 * step_width;
            let step_rect = Rect::from_min_size(
                Pos2::new(x, rect.min.y),
                Vec2::new(step_width + 1.0, rect.height()),
            );
            
            painter.rect_filled(step_rect, Rounding::ZERO, color);
        }
    }
    
    fn draw_vertical_gradient(&self, painter: &Painter, rect: Rect) {
        let steps = 50;
        let step_height = rect.height() / steps as f32;
        
        for i in 0..steps {
            let progress = i as f32 / (steps - 1) as f32;
            let color = self.interpolate_color(progress);
            
            let y = rect.min.y + i as f32 * step_height;
            let step_rect = Rect::from_min_size(
                Pos2::new(rect.min.x, y),
                Vec2::new(rect.width(), step_height + 1.0),
            );
            
            painter.rect_filled(step_rect, Rounding::ZERO, color);
        }
    }
    
    fn draw_diagonal_gradient(&self, painter: &Painter, rect: Rect) {
        let steps = 100;
        let diagonal_length = (rect.width().powi(2) + rect.height().powi(2)).sqrt();
        let step_size = diagonal_length / steps as f32;
        
        for i in 0..steps {
            let progress = i as f32 / (steps - 1) as f32;
            let color = self.interpolate_color(progress);
            
            // 绘制对角线条带
            let offset = i as f32 * step_size / diagonal_length;
            self.draw_diagonal_stripe(painter, rect, offset, step_size / diagonal_length, color);
        }
    }
    
    fn draw_radial_gradient(&self, painter: &Painter, rect: Rect) {
        let center = rect.center();
        let max_radius = (rect.width().max(rect.height())) / 2.0;
        let steps = 50;
        
        for i in (0..steps).rev() {
            let progress = i as f32 / (steps - 1) as f32;
            let color = self.interpolate_color(progress);
            let radius = max_radius * (i + 1) as f32 / steps as f32;
            
            painter.circle_filled(center, radius, color);
        }
    }
    
    fn draw_diagonal_stripe(&self, painter: &Painter, rect: Rect, offset: f32, width: f32, color: Color32) {
        // 简化的对角线条带绘制
        let points = vec![
            rect.min,
            Pos2::new(rect.max.x, rect.min.y),
            rect.max,
            Pos2::new(rect.min.x, rect.max.y),
        ];
        
        // 这里简化处理，实际应该计算精确的对角线条带
        painter.rect_filled(rect, Rounding::ZERO, color);
    }
    
    fn interpolate_color(&self, progress: f32) -> Color32 {
        if self.colors.len() < 2 {
            return self.colors[0];
        }
        
        let segment_size = 1.0 / (self.colors.len() - 1) as f32;
        let segment_index = (progress / segment_size).floor() as usize;
        let segment_progress = (progress % segment_size) / segment_size;
        
        let start_index = segment_index.min(self.colors.len() - 2);
        let end_index = start_index + 1;
        
        let start_color = self.colors[start_index];
        let end_color = self.colors[end_index];
        
        let r = (start_color.r() as f32 * (1.0 - segment_progress) + end_color.r() as f32 * segment_progress) as u8;
        let g = (start_color.g() as f32 * (1.0 - segment_progress) + end_color.g() as f32 * segment_progress) as u8;
        let b = (start_color.b() as f32 * (1.0 - segment_progress) + end_color.b() as f32 * segment_progress) as u8;
        let a = ((start_color.a() as f32 * (1.0 - segment_progress) + end_color.a() as f32 * segment_progress) * self.opacity) as u8;
        
        Color32::from_rgba_premultiplied(r, g, b, a)
    }
}

/// 霓虹发光效果
pub struct NeonGlow {
    color: Color32,
    intensity: f32,
    blur_radius: f32,
}

impl NeonGlow {
    pub fn new(color: Color32) -> Self {
        Self {
            color,
            intensity: 1.0,
            blur_radius: 8.0,
        }
    }
    
    pub fn with_intensity(mut self, intensity: f32) -> Self {
        self.intensity = intensity.clamp(0.0, 2.0);
        self
    }
    
    pub fn with_blur_radius(mut self, radius: f32) -> Self {
        self.blur_radius = radius.max(0.0);
        self
    }
    
    /// 绘制霓虹发光边框
    pub fn draw_border(&self, painter: &Painter, rect: Rect, rounding: Rounding, stroke_width: f32) {
        // 绘制多层发光效果
        let layers = 5;
        for i in 0..layers {
            let layer_alpha = (self.color.a() as f32 * self.intensity * (1.0 - i as f32 / layers as f32) / layers as f32) as u8;
            let layer_color = Color32::from_rgba_premultiplied(
                self.color.r(),
                self.color.g(),
                self.color.b(),
                layer_alpha,
            );
            
            let layer_width = stroke_width + (i as f32 * self.blur_radius / layers as f32);
            let expanded_rect = rect.expand(i as f32 * 2.0);
            
            painter.rect_stroke(expanded_rect, rounding, Stroke::new(layer_width, layer_color));
        }
    }
    
    /// 绘制霓虹发光文字
    pub fn draw_text(&self, painter: &Painter, pos: Pos2, text: &str, font_id: egui::FontId) {
        // 绘制发光效果
        let layers = 3;
        for i in 0..layers {
            let layer_alpha = (self.color.a() as f32 * self.intensity * (1.0 - i as f32 / layers as f32) / layers as f32) as u8;
            let layer_color = Color32::from_rgba_premultiplied(
                self.color.r(),
                self.color.g(),
                self.color.b(),
                layer_alpha,
            );
            
            let offset = i as f32 * 0.5;
            painter.text(
                pos + Vec2::new(offset, offset),
                egui::Align2::LEFT_TOP,
                text,
                font_id.clone(),
                layer_color,
            );
        }
        
        // 绘制主文字
        painter.text(pos, egui::Align2::LEFT_TOP, text, font_id, Color32::WHITE);
    }
}

/// 玻璃拟态效果
pub struct GlassMorphism {
    background_color: Color32,
    border_color: Color32,
    blur_strength: f32,
}

impl GlassMorphism {
    pub fn new(palette: &ColorPalette) -> Self {
        Self {
            background_color: palette.glass_bg,
            border_color: Color32::from_rgba_premultiplied(255, 255, 255, 30),
            blur_strength: 1.0,
        }
    }
    
    pub fn with_blur_strength(mut self, strength: f32) -> Self {
        self.blur_strength = strength.clamp(0.0, 2.0);
        self
    }
    
    /// 绘制玻璃拟态卡片
    pub fn draw_card(&self, painter: &Painter, rect: Rect, rounding: Rounding) {
        // 绘制半透明背景
        painter.rect_filled(rect, rounding, self.background_color);
        
        // 绘制边框高光
        painter.rect_stroke(
            rect,
            rounding,
            Stroke::new(1.0, self.border_color),
        );
        
        // 绘制顶部高光
        let highlight_rect = Rect::from_min_size(
            rect.min,
            Vec2::new(rect.width(), rect.height() * 0.3),
        );
        let highlight_color = Color32::from_rgba_premultiplied(255, 255, 255, 10);
        painter.rect_filled(highlight_rect, rounding, highlight_color);
    }
}

/// 动态粒子效果（简化版）
pub struct ParticleEffect {
    particles: Vec<Particle>,
    color: Color32,
}

#[derive(Debug, Clone)]
struct Particle {
    position: Pos2,
    velocity: Vec2,
    life: f32,
    max_life: f32,
}

impl ParticleEffect {
    pub fn new(color: Color32) -> Self {
        Self {
            particles: Vec::new(),
            color,
        }
    }
    
    pub fn spawn_particle(&mut self, position: Pos2, velocity: Vec2) {
        self.particles.push(Particle {
            position,
            velocity,
            life: 1.0,
            max_life: 1.0,
        });
    }
    
    pub fn update(&mut self, dt: f32) {
        self.particles.retain_mut(|particle| {
            particle.position += particle.velocity * dt;
            particle.life -= dt;
            particle.life > 0.0
        });
    }
    
    pub fn draw(&self, painter: &Painter) {
        for particle in &self.particles {
            let alpha = (particle.life / particle.max_life * 255.0) as u8;
            let color = Color32::from_rgba_premultiplied(
                self.color.r(),
                self.color.g(),
                self.color.b(),
                alpha,
            );
            
            painter.circle_filled(particle.position, 2.0, color);
        }
    }
}
