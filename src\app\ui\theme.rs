/// 现代化主题系统
///
/// 提供专业的颜色主题、样式定义和视觉效果
use eframe::egui::{self, Color32, Rounding, Shadow, Stroke, Visuals};

/// 主题类型
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ThemeType {
    /// 深色主题 - 专业高级
    Dark,
    /// 浅色主题 - 清新简洁
    Light,
}

/// 现代化颜色调色板
#[derive(Debug, Clone)]
pub struct ColorPalette {
    // 主色调 - 品牌色
    pub primary: Color32,
    pub primary_hover: Color32,
    pub primary_pressed: Color32,

    // 辅助色
    pub secondary: Color32,
    pub secondary_hover: Color32,

    // 强调色
    pub accent: Color32,
    pub accent_hover: Color32,

    // 成功/警告/错误色
    pub success: Color32,
    pub warning: Color32,
    pub error: Color32,
    pub info: Color32,

    // 背景色
    pub background: Color32,
    pub surface: Color32,
    pub surface_hover: Color32,
    pub surface_pressed: Color32,

    // 文本色
    pub text_primary: Color32,
    pub text_secondary: Color32,
    pub text_disabled: Color32,
    pub text_on_primary: Color32,

    // 边框色
    pub border: Color32,
    pub border_light: Color32,
    pub border_focus: Color32,

    // 阴影色
    pub shadow: Color32,

    // 炫酷特效色
    pub neon_pink: Color32,
    pub neon_green: Color32,
    pub neon_orange: Color32,
    pub glass_bg: Color32,
}

impl ColorPalette {
    /// 创建深色主题调色板
    pub fn dark() -> Self {
        Self {
            // 主色调 - 霓虹蓝紫渐变
            primary: Color32::from_rgb(99, 102, 241), // #6366F1 - 现代紫蓝
            primary_hover: Color32::from_rgb(129, 140, 248), // #818CF8 - 亮紫蓝
            primary_pressed: Color32::from_rgb(79, 70, 229), // #4F46E5 - 深紫蓝

            // 辅助色 - 霓虹紫色
            secondary: Color32::from_rgb(168, 85, 247), // #A855F7 - 霓虹紫
            secondary_hover: Color32::from_rgb(196, 125, 249), // #C47DF9 - 亮霓虹紫

            // 强调色 - 霓虹青色
            accent: Color32::from_rgb(6, 182, 212), // #06B6D4 - 霓虹青
            accent_hover: Color32::from_rgb(34, 211, 238), // #22D3EE - 亮霓虹青

            // 状态色 - 霓虹配色
            success: Color32::from_rgb(16, 185, 129), // #10B981 - 霓虹绿
            warning: Color32::from_rgb(245, 158, 11), // #F59E0B - 霓虹橙
            error: Color32::from_rgb(239, 68, 68),    // #EF4444 - 霓虹红
            info: Color32::from_rgb(59, 130, 246),    // #3B82F6 - 霓虹蓝

            // 背景色 - 深空渐变系
            background: Color32::from_rgb(3, 7, 18), // #030712 - 深空蓝
            surface: Color32::from_rgb(15, 23, 42),  // #0F172A - 深蓝灰
            surface_hover: Color32::from_rgb(30, 41, 59), // #1E293B - 中蓝灰
            surface_pressed: Color32::from_rgb(51, 65, 85), // #334155 - 浅蓝灰

            // 文本色
            text_primary: Color32::from_rgb(248, 250, 252), // #F8FAFC
            text_secondary: Color32::from_rgb(148, 163, 184), // #94A3B8
            text_disabled: Color32::from_rgb(100, 116, 139), // #64748B
            text_on_primary: Color32::WHITE,

            // 边框色 - 霓虹效果
            border: Color32::from_rgb(51, 65, 85), // #334155 - 基础边框
            border_light: Color32::from_rgb(71, 85, 105), // #475569 - 浅边框
            border_focus: Color32::from_rgb(99, 102, 241), // #6366F1 - 霓虹焦点边框

            // 阴影色 - 霓虹发光
            shadow: Color32::from_rgba_premultiplied(99, 102, 241, 60), // 霓虹蓝发光

            // 炫酷特效色
            neon_pink: Color32::from_rgb(236, 72, 153), // #EC4899 - 霓虹粉
            neon_green: Color32::from_rgb(34, 197, 94), // #22C55E - 霓虹绿
            neon_orange: Color32::from_rgb(251, 146, 60), // #FB923C - 霓虹橙
            glass_bg: Color32::from_rgba_premultiplied(15, 23, 42, 180), // 玻璃效果背景
        }
    }

    /// 创建浅色主题调色板
    pub fn light() -> Self {
        Self {
            // 主色调 - 现代蓝色
            primary: Color32::from_rgb(59, 130, 246), // #3B82F6
            primary_hover: Color32::from_rgb(37, 99, 235), // #2563EB
            primary_pressed: Color32::from_rgb(29, 78, 216), // #1D4ED8

            // 辅助色 - 紫色
            secondary: Color32::from_rgb(139, 92, 246), // #8B5CF6
            secondary_hover: Color32::from_rgb(124, 58, 237), // #7C3AED

            // 强调色 - 青色
            accent: Color32::from_rgb(16, 185, 129), // #10B981
            accent_hover: Color32::from_rgb(5, 150, 105), // #059669

            // 状态色
            success: Color32::from_rgb(16, 185, 129), // #10B981
            warning: Color32::from_rgb(245, 158, 11), // #F59E0B
            error: Color32::from_rgb(239, 68, 68),    // #EF4444
            info: Color32::from_rgb(59, 130, 246),    // #3B82F6

            // 背景色 - 浅色系
            background: Color32::from_rgb(248, 250, 252), // #F8FAFC
            surface: Color32::WHITE,                      // #FFFFFF
            surface_hover: Color32::from_rgb(248, 250, 252), // #F8FAFC
            surface_pressed: Color32::from_rgb(241, 245, 249), // #F1F5F9

            // 文本色
            text_primary: Color32::from_rgb(15, 23, 42), // #0F172A
            text_secondary: Color32::from_rgb(71, 85, 105), // #475569
            text_disabled: Color32::from_rgb(148, 163, 184), // #94A3B8
            text_on_primary: Color32::WHITE,

            // 边框色
            border: Color32::from_rgb(226, 232, 240), // #E2E8F0
            border_light: Color32::from_rgb(241, 245, 249), // #F1F5F9
            border_focus: Color32::from_rgb(59, 130, 246), // #3B82F6

            // 阴影色
            shadow: Color32::from_rgba_premultiplied(0, 0, 0, 15),

            // 炫酷特效色
            neon_pink: Color32::from_rgb(219, 39, 119), // #DB2777 - 深霓虹粉
            neon_green: Color32::from_rgb(16, 185, 129), // #10B981 - 深霓虹绿
            neon_orange: Color32::from_rgb(234, 88, 12), // #EA580C - 深霓虹橙
            glass_bg: Color32::from_rgba_premultiplied(255, 255, 255, 200), // 浅色玻璃效果
        }
    }
}

/// 现代化样式定义
#[derive(Debug, Clone)]
pub struct ModernStyle {
    pub colors: ColorPalette,

    // 圆角设置
    pub rounding_small: Rounding,
    pub rounding_medium: Rounding,
    pub rounding_large: Rounding,

    // 阴影设置
    pub shadow_small: Shadow,
    pub shadow_medium: Shadow,
    pub shadow_large: Shadow,

    // 描边设置
    pub stroke_thin: Stroke,
    pub stroke_medium: Stroke,
    pub stroke_thick: Stroke,
}

impl ModernStyle {
    /// 创建深色主题样式
    pub fn dark() -> Self {
        let colors = ColorPalette::dark();

        Self {
            colors: colors.clone(),

            // 圆角 - 现代化设计
            rounding_small: Rounding::same(6.0),
            rounding_medium: Rounding::same(12.0),
            rounding_large: Rounding::same(16.0),

            // 阴影 - 增加深度感
            shadow_small: Shadow {
                offset: egui::vec2(0.0, 2.0),
                blur: 4.0,
                spread: 0.0,
                color: colors.shadow,
            },
            shadow_medium: Shadow {
                offset: egui::vec2(0.0, 4.0),
                blur: 8.0,
                spread: 0.0,
                color: colors.shadow,
            },
            shadow_large: Shadow {
                offset: egui::vec2(0.0, 8.0),
                blur: 16.0,
                spread: 0.0,
                color: colors.shadow,
            },

            // 描边
            stroke_thin: Stroke::new(1.0, colors.border),
            stroke_medium: Stroke::new(2.0, colors.border),
            stroke_thick: Stroke::new(3.0, colors.border_focus),
        }
    }

    /// 创建浅色主题样式
    pub fn light() -> Self {
        let colors = ColorPalette::light();

        Self {
            colors: colors.clone(),

            // 圆角
            rounding_small: Rounding::same(6.0),
            rounding_medium: Rounding::same(12.0),
            rounding_large: Rounding::same(16.0),

            // 阴影
            shadow_small: Shadow {
                offset: egui::vec2(0.0, 1.0),
                blur: 3.0,
                spread: 0.0,
                color: colors.shadow,
            },
            shadow_medium: Shadow {
                offset: egui::vec2(0.0, 4.0),
                blur: 6.0,
                spread: -1.0,
                color: colors.shadow,
            },
            shadow_large: Shadow {
                offset: egui::vec2(0.0, 10.0),
                blur: 15.0,
                spread: -3.0,
                color: colors.shadow,
            },

            // 描边
            stroke_thin: Stroke::new(1.0, colors.border),
            stroke_medium: Stroke::new(2.0, colors.border),
            stroke_thick: Stroke::new(3.0, colors.border_focus),
        }
    }
}

/// 主题管理器
#[derive(Clone)]
pub struct ThemeManager {
    current_theme: ThemeType,
    dark_style: ModernStyle,
    light_style: ModernStyle,
}

impl ThemeManager {
    pub fn new() -> Self {
        Self {
            current_theme: ThemeType::Dark,
            dark_style: ModernStyle::dark(),
            light_style: ModernStyle::light(),
        }
    }

    /// 获取当前主题类型
    pub fn current_theme(&self) -> ThemeType {
        self.current_theme
    }

    /// 切换主题
    pub fn toggle_theme(&mut self) {
        self.current_theme = match self.current_theme {
            ThemeType::Dark => ThemeType::Light,
            ThemeType::Light => ThemeType::Dark,
        };
    }

    /// 设置主题
    pub fn set_theme(&mut self, theme: ThemeType) {
        self.current_theme = theme;
    }

    /// 获取当前样式
    pub fn current_style(&self) -> &ModernStyle {
        match self.current_theme {
            ThemeType::Dark => &self.dark_style,
            ThemeType::Light => &self.light_style,
        }
    }

    /// 应用主题到egui上下文
    pub fn apply_to_context(&self, ctx: &egui::Context) {
        let style = self.current_style();
        let mut visuals = match self.current_theme {
            ThemeType::Dark => Visuals::dark(),
            ThemeType::Light => Visuals::light(),
        };

        // 应用自定义颜色
        self.customize_visuals(&mut visuals, style);

        ctx.set_visuals(visuals);
    }

    /// 自定义视觉样式
    fn customize_visuals(&self, visuals: &mut Visuals, style: &ModernStyle) {
        let colors = &style.colors;

        // 背景色
        visuals.panel_fill = colors.surface;
        visuals.window_fill = colors.surface;
        visuals.extreme_bg_color = colors.background;

        // 文本色
        visuals.override_text_color = Some(colors.text_primary);
        // visuals.weak_text_color = colors.text_secondary; // 这个字段可能不存在，先注释掉

        // 控件颜色
        visuals.widgets.noninteractive.bg_fill = colors.surface;
        visuals.widgets.noninteractive.fg_stroke = Stroke::new(1.0, colors.text_secondary);

        visuals.widgets.inactive.bg_fill = colors.surface_hover;
        visuals.widgets.inactive.fg_stroke = Stroke::new(1.0, colors.text_primary);
        visuals.widgets.inactive.rounding = style.rounding_small;

        visuals.widgets.hovered.bg_fill = colors.primary_hover;
        visuals.widgets.hovered.fg_stroke = Stroke::new(1.0, colors.text_on_primary);
        visuals.widgets.hovered.rounding = style.rounding_small;

        visuals.widgets.active.bg_fill = colors.primary_pressed;
        visuals.widgets.active.fg_stroke = Stroke::new(1.0, colors.text_on_primary);
        visuals.widgets.active.rounding = style.rounding_small;

        visuals.widgets.open.bg_fill = colors.primary;
        visuals.widgets.open.fg_stroke = Stroke::new(1.0, colors.text_on_primary);
        visuals.widgets.open.rounding = style.rounding_small;

        // 选择颜色
        visuals.selection.bg_fill = colors.primary;
        visuals.selection.stroke = Stroke::new(1.0, colors.primary);

        // 超链接颜色
        visuals.hyperlink_color = colors.accent;

        // 窗口阴影
        visuals.window_shadow = style.shadow_large;
        visuals.popup_shadow = style.shadow_medium;

        // 窗口圆角
        visuals.window_rounding = style.rounding_medium;
        visuals.menu_rounding = style.rounding_small;
    }
}

impl Default for ThemeManager {
    fn default() -> Self {
        Self::new()
    }
}
