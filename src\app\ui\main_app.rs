use super::components::{
    ButtonSize, ButtonVariant, CardElevation, ModernButton, ModernCard, ModernProgressBar,
    ModernTabs,
};
use super::effects::{GlassMorphism, GradientBackground, GradientDirection, NeonGlow};
use super::theme::{ThemeManager, ThemeType};
use crate::core::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sk<PERSON>canner, ScanResult};
use crate::utils::file_utils::{get_display_path, get_parent_dir_display, open_in_explorer};
use crate::utils::format::{format_number, format_size};
use eframe::egui::{self, RichText};
use std::sync::mpsc::{self, Receiver, Sender};
use std::sync::{Arc, Mutex};
use std::thread;

/// 主应用程序结构
pub struct DiskCleanerApp {
    /// 磁盘扫描器
    scanner: Arc<Mutex<DiskScanner>>,
    /// 磁盘清理器
    cleaner: DiskCleaner,
    /// 当前扫描结果
    scan_result: Option<ScanResult>,
    /// 是否正在扫描
    is_scanning: bool,
    /// 选中的扫描路径
    selected_path: String,
    /// 界面状态
    ui_state: UiState,
    /// 扫描结果接收器
    scan_receiver: Option<Receiver<ScanResult>>,
    /// 进度接收器
    progress_receiver: Option<Receiver<crate::core::scanner::ScanProgress>>,
    /// 扫描进度
    scan_progress: f32,
    /// 扫描状态消息
    scan_status: String,
    /// 主题管理器
    theme_manager: ThemeManager,
}

#[derive(Default)]
struct UiState {
    /// 当前选中的标签页
    current_tab: TabType,
    /// 是否显示隐藏文件
    show_hidden_files: bool,
    /// 最小文件大小过滤器（MB）
    min_file_size_mb: f32,
    /// 搜索关键词
    search_keyword: String,
    /// 是否显示垃圾文件详情
    show_junk_details: bool,
}

#[derive(Default, PartialEq)]
enum TabType {
    #[default]
    Analysis, // 磁盘分析
    Cleanup,  // 清理工具
    Settings, // 设置
}

impl DiskCleanerApp {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        // 创建主题管理器
        let theme_manager = ThemeManager::new();

        // 应用主题到上下文
        theme_manager.apply_to_context(&cc.egui_ctx);

        Self {
            scanner: Arc::new(Mutex::new(DiskScanner::new())),
            cleaner: DiskCleaner::new(),
            scan_result: None,
            is_scanning: false,
            selected_path: "C:\\".to_string(),
            ui_state: UiState::default(),
            scan_receiver: None,
            progress_receiver: None,
            scan_progress: 0.0,
            scan_status: "就绪".to_string(),
            theme_manager,
        }
    }

    /// 开始扫描磁盘
    fn start_scan(&mut self) {
        if self.is_scanning {
            return;
        }

        log::info!("开始扫描路径: {}", self.selected_path);

        self.is_scanning = true;
        self.scan_result = None;
        self.scan_progress = 0.0;
        self.scan_status = "正在初始化扫描...".to_string();

        // 创建通道用于接收扫描结果
        let (result_sender, result_receiver) = mpsc::channel();
        self.scan_receiver = Some(result_receiver);

        // 创建通道用于接收进度更新
        let (progress_sender, progress_receiver) = mpsc::channel();
        self.progress_receiver = Some(progress_receiver);

        // 克隆需要的数据
        let scanner = Arc::clone(&self.scanner);
        let path = self.selected_path.clone();

        // 在新线程中执行扫描
        thread::spawn(move || {
            // 检查路径是否存在
            if !std::path::Path::new(&path).exists() {
                log::error!("路径不存在: {}", path);
                return;
            }

            match scanner.lock() {
                Ok(scanner) => {
                    // 使用带进度回调的扫描方法
                    match scanner.scan_with_progress(&path, |progress| {
                        // 发送进度更新
                        if let Err(e) = progress_sender.send(progress) {
                            log::error!("发送进度更新失败: {}", e);
                        }
                    }) {
                        Ok(result) => {
                            log::info!(
                                "扫描完成: {} 个文件, {} 个文件夹, {} 字节",
                                result.file_count,
                                result.folder_count,
                                result.total_size
                            );
                            if let Err(e) = result_sender.send(result) {
                                log::error!("发送扫描结果失败: {}", e);
                            }
                        }
                        Err(e) => {
                            log::error!("扫描失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    log::error!("获取扫描器锁失败: {}", e);
                }
            }
        });
    }

    /// 渲染顶部工具栏
    fn render_toolbar(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            ui.label("扫描路径:");
            ui.text_edit_singleline(&mut self.selected_path);

            if ui.button("📁 选择文件夹").clicked() {
                self.show_folder_dialog();
            }

            ui.separator();

            if self.is_scanning {
                ui.add(egui::Spinner::new());
                ui.label("正在扫描...");
                if ui.button("停止").clicked() {
                    self.is_scanning = false;
                    self.scan_status = "已停止".to_string();
                }
            } else {
                if ui.button("🔍 开始扫描").clicked() {
                    self.start_scan();
                }
            }
        });

        // 显示扫描进度条
        if self.is_scanning || self.scan_progress > 0.0 {
            ui.group(|ui| {
                ui.vertical(|ui| {
                    ui.horizontal(|ui| {
                        ui.label("🔍 扫描进度:");
                        ui.add(
                            egui::ProgressBar::new(self.scan_progress)
                                .show_percentage()
                                .desired_width(200.0),
                        );

                        if self.is_scanning {
                            ui.spinner();
                        }
                    });

                    // 显示详细状态
                    ui.horizontal(|ui| {
                        ui.add_space(10.0);
                        ui.label(
                            egui::RichText::new(&self.scan_status)
                                .size(12.0)
                                .color(egui::Color32::GRAY),
                        );
                    });
                });
            });
        }
    }

    /// 渲染标签页
    fn render_tabs(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            ui.selectable_value(
                &mut self.ui_state.current_tab,
                TabType::Analysis,
                "📊 磁盘分析",
            );
            ui.selectable_value(
                &mut self.ui_state.current_tab,
                TabType::Cleanup,
                "🧹 清理工具",
            );
            ui.selectable_value(&mut self.ui_state.current_tab, TabType::Settings, "⚙️ 设置");
        });
        ui.separator();
    }

    /// 渲染磁盘分析页面
    fn render_analysis_tab(&mut self, ui: &mut egui::Ui) {
        // 克隆需要的数据以避免借用冲突
        let scan_result = self.scan_result.clone();

        if let Some(result) = &scan_result {
            // 顶部概览卡片
            ui.horizontal(|ui| {
                // 总体统计卡片
                ui.group(|ui| {
                    ui.vertical(|ui| {
                        ui.heading("📊 总体统计");
                        ui.label(format!("📁 总大小: {}", format_size(result.total_size)));
                        ui.label(format!("📄 文件数量: {}", format_number(result.file_count)));
                        ui.label(format!(
                            "📂 文件夹数量: {}",
                            format_number(result.folder_count)
                        ));
                        ui.label(format!("⏱️ 扫描耗时: {} 毫秒", result.scan_duration_ms));
                    });
                });

                ui.separator();

                // 垃圾文件统计卡片
                ui.group(|ui| {
                    ui.vertical(|ui| {
                        ui.heading("🗑️ 垃圾文件分析");
                        ui.label(format!(
                            "🔢 垃圾文件数量: {}",
                            format_number(result.junk_file_count)
                        ));
                        ui.label(format!(
                            "💾 垃圾文件大小: {}",
                            format_size(result.junk_file_size)
                        ));
                        let percentage = if result.total_size > 0 {
                            (result.junk_file_size as f64 / result.total_size as f64) * 100.0
                        } else {
                            0.0
                        };
                        ui.label(format!("📈 占总空间: {:.1}%", percentage));

                        if result.junk_file_size > 0 {
                            ui.add_space(5.0);
                            if ui.button("🧹 查看详细分类").clicked() {
                                self.ui_state.show_junk_details = true;
                            }
                        }
                    });
                });

                ui.separator();

                // 大文件统计卡片
                ui.group(|ui| {
                    ui.vertical(|ui| {
                        ui.heading("📄 大文件分析");
                        ui.label(format!("🔢 大文件数量: {}", result.large_files.len()));
                        let large_files_size: u64 = result.large_files.iter().map(|f| f.size).sum();
                        ui.label(format!(
                            "💾 大文件总大小: {}",
                            format_size(large_files_size)
                        ));
                        let percentage = if result.total_size > 0 {
                            (large_files_size as f64 / result.total_size as f64) * 100.0
                        } else {
                            0.0
                        };
                        ui.label(format!("📈 占总空间: {:.1}%", percentage));
                    });
                });
            });

            ui.add_space(10.0);

            // 详细分析区域 - 改进的两列布局
            ui.columns(2, |columns| {
                // 左列：文件夹占用分析
                columns[0].group(|ui| {
                    ui.vertical(|ui| {
                        ui.heading("📁 文件夹占用分析");
                        ui.label("💡 显示占用空间最大的文件夹");
                        ui.separator();

                        if result.folders.is_empty() {
                            ui.label("暂无文件夹数据");
                        } else {
                            for (i, folder) in result.folders.iter().take(8).enumerate() {
                                ui.group(|ui| {
                                    ui.vertical(|ui| {
                                        // 第一行：文件夹名称和大小
                                        ui.horizontal(|ui| {
                                            ui.label(&(i + 1).to_string());
                                            ui.label("📁");

                                            // 文件夹名称截断（安全处理中文字符）
                                            let display_name = if folder.name.chars().count() > 25 {
                                                let chars: Vec<char> =
                                                    folder.name.chars().collect();
                                                chars.iter().take(22).collect::<String>() + "..."
                                            } else {
                                                folder.name.clone()
                                            };
                                            ui.label(display_name);

                                            ui.with_layout(
                                                egui::Layout::right_to_left(egui::Align::Center),
                                                |ui| {
                                                    // 打开文件夹按钮
                                                    if ui.small_button("📂 打开").clicked() {
                                                        if let Err(_e) =
                                                            open_in_explorer(&folder.path)
                                                        {
                                                            // 错误处理已简化
                                                        }
                                                    }
                                                    ui.label(format_size(folder.size));
                                                },
                                            );
                                        });

                                        // 第二行：文件夹路径
                                        ui.horizontal(|ui| {
                                            ui.add_space(20.0); // 缩进对齐
                                            ui.label("📍");

                                            let parent_path = get_parent_dir_display(&folder.path);
                                            let display_path = get_display_path(
                                                &std::path::Path::new(&parent_path),
                                                35,
                                            );

                                            ui.label(
                                                egui::RichText::new(display_path)
                                                    .size(11.0)
                                                    .color(egui::Color32::GRAY),
                                            );
                                        });
                                    });
                                });

                                if i < 7 && i < result.folders.len() - 1 {
                                    ui.add_space(3.0);
                                }
                            }

                            if result.folders.len() > 8 {
                                ui.add_space(5.0);
                                ui.label("还有更多文件夹...");
                            }
                        }
                    });
                });

                // 右列：大文件分析
                columns[1].group(|ui| {
                    ui.vertical(|ui| {
                        ui.heading("📄 大文件分析");
                        ui.label("💡 显示单个大文件，便于清理");
                        ui.separator();

                        if result.large_files.is_empty() {
                            ui.label("未发现大文件 (>100MB)");
                        } else {
                            for (i, file) in result.large_files.iter().take(8).enumerate() {
                                ui.group(|ui| {
                                    ui.vertical(|ui| {
                                        // 第一行：文件名和大小
                                        ui.horizontal(|ui| {
                                            ui.label(&(i + 1).to_string());

                                            // 文件图标
                                            let icon = match file.extension.as_deref() {
                                                Some("exe") => "⚙️",
                                                Some("zip") | Some("rar") | Some("7z") => "📦",
                                                Some("mp4") | Some("avi") | Some("mkv") => "🎬",
                                                Some("mp3") | Some("wav") | Some("flac") => "🎵",
                                                Some("jpg") | Some("png") | Some("gif") => "🖼️",
                                                Some("pdf") => "📄",
                                                Some("doc") | Some("docx") => "📝",
                                                _ => "📄",
                                            };
                                            ui.label(icon);

                                            // 文件名截断（安全处理中文字符）
                                            let display_name = if file.name.chars().count() > 25 {
                                                let chars: Vec<char> = file.name.chars().collect();
                                                chars.iter().take(22).collect::<String>() + "..."
                                            } else {
                                                file.name.clone()
                                            };
                                            ui.label(display_name);

                                            ui.with_layout(
                                                egui::Layout::right_to_left(egui::Align::Center),
                                                |ui| {
                                                    // 打开文件位置按钮
                                                    if ui.small_button("📂 打开位置").clicked()
                                                    {
                                                        if let Err(_e) =
                                                            open_in_explorer(&file.path)
                                                        {
                                                            // 错误处理已简化
                                                        }
                                                    }
                                                    ui.label(format_size(file.size));
                                                },
                                            );
                                        });

                                        // 第二行：文件路径
                                        ui.horizontal(|ui| {
                                            ui.add_space(20.0); // 缩进对齐
                                            ui.label("📍");

                                            let parent_path = get_parent_dir_display(&file.path);
                                            let display_path = get_display_path(
                                                &std::path::Path::new(&parent_path),
                                                35,
                                            );

                                            ui.label(
                                                egui::RichText::new(display_path)
                                                    .size(11.0)
                                                    .color(egui::Color32::GRAY),
                                            );

                                            ui.with_layout(
                                                egui::Layout::right_to_left(egui::Align::Center),
                                                |ui| {
                                                    // 显示文件类型
                                                    if let Some(ext) = &file.extension {
                                                        ui.label(
                                                            egui::RichText::new(
                                                                &ext.to_uppercase(),
                                                            )
                                                            .size(10.0)
                                                            .color(egui::Color32::GRAY),
                                                        );
                                                    }
                                                },
                                            );
                                        });
                                    });
                                });

                                if i < 7 && i < result.large_files.len() - 1 {
                                    ui.add_space(3.0);
                                }
                            }

                            if result.large_files.len() > 8 {
                                ui.add_space(5.0);
                                ui.label("还有更多大文件...");
                            }
                        }
                    });
                });
            });
        } else {
            ui.vertical_centered(|ui| {
                ui.add_space(100.0);
                ui.heading("🔍 开始磁盘分析");
                ui.add_space(20.0);
                ui.label("选择要分析的文件夹，然后点击开始扫描");
                ui.add_space(20.0);
                if ui.button("🚀 开始扫描").clicked() {
                    self.start_scan();
                }
            });
        }
    }

    /// 渲染文件夹树状图
    fn render_folder_tree(&self, ui: &mut egui::Ui, result: &ScanResult) {
        ui.label("文件夹大小排序 (前10个):");
        ui.separator();

        for (i, folder) in result.folders.iter().take(10).enumerate() {
            ui.group(|ui| {
                ui.vertical(|ui| {
                    // 第一行：文件夹名称和大小
                    ui.horizontal(|ui| {
                        ui.label(format!("{}.", i + 1));

                        let folder_icon = if folder.subfolder_count > 0 {
                            "📁"
                        } else {
                            "📂"
                        };
                        ui.label(folder_icon);

                        // 文件夹名称（截断显示，安全处理中文字符）
                        let display_name = if folder.name.chars().count() > 30 {
                            let chars: Vec<char> = folder.name.chars().collect();
                            chars.iter().take(27).collect::<String>() + "..."
                        } else {
                            folder.name.clone()
                        };
                        ui.label(display_name);

                        ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                            // 打开文件夹按钮
                            if ui.small_button("📂 打开文件夹").clicked() {
                                if let Err(e) = open_in_explorer(&folder.path) {
                                    log::error!("打开文件夹失败: {}", e);
                                }
                            }

                            ui.label(format_size(folder.size));
                            ui.label(format!("({} 文件)", folder.file_count));
                        });
                    });

                    // 第二行：文件夹路径
                    ui.horizontal(|ui| {
                        ui.add_space(20.0); // 缩进对齐
                        ui.label("📍");

                        let display_path = get_display_path(&folder.path, 60);
                        ui.label(
                            egui::RichText::new(display_path)
                                .size(11.0)
                                .color(egui::Color32::GRAY),
                        );
                    });
                });
            });

            if i < result.folders.len().min(10) - 1 {
                ui.add_space(3.0);
            }
        }

        if result.folders.len() > 10 {
            ui.label(format!("... 还有 {} 个文件夹", result.folders.len() - 10));
        }
    }

    /// 渲染统计信息
    fn render_statistics(&self, ui: &mut egui::Ui, result: &ScanResult) {
        ui.label(format!("总大小: {}", format_size(result.total_size)));
        ui.label(format!("文件数量: {}", result.file_count));
        ui.label(format!("文件夹数量: {}", result.folder_count));
        ui.label(format!("扫描耗时: {} 毫秒", result.scan_duration_ms));
        ui.separator();

        // 显示垃圾文件统计
        ui.label("🗑️ 垃圾文件分析:");
        ui.label(format!("  垃圾文件数量: {}", result.junk_file_count));
        ui.label(format!(
            "  垃圾文件大小: {}",
            format_size(result.junk_file_size)
        ));
        ui.separator();

        if !result.large_files.is_empty() {
            ui.label("📄 最大文件:");
            for (i, file) in result.large_files.iter().take(5).enumerate() {
                ui.label(format!(
                    "  {}. {} ({})",
                    i + 1,
                    file.name,
                    format_size(file.size)
                ));
            }
        }

        ui.separator();

        if !result.folders.is_empty() {
            ui.label("📁 最大文件夹:");
            for (i, folder) in result.folders.iter().take(5).enumerate() {
                ui.label(format!(
                    "  {}. {} ({})",
                    i + 1,
                    folder.name,
                    format_size(folder.size)
                ));
            }
        }
    }

    /// 渲染清理工具页面
    fn render_cleanup_tab(&mut self, ui: &mut egui::Ui) {
        ui.heading("系统清理工具");
        ui.separator();

        ui.group(|ui| {
            ui.label("🗑️ 临时文件清理");
            ui.horizontal(|ui| {
                if ui.button("扫描临时文件").clicked() {
                    // 后续实现
                }
                ui.label("预计可清理: 计算中...");
            });
        });

        ui.group(|ui| {
            ui.label("🌐 浏览器缓存清理");
            ui.horizontal(|ui| {
                if ui.button("扫描浏览器缓存").clicked() {
                    // 后续实现
                }
                ui.label("预计可清理: 计算中...");
            });
        });

        ui.group(|ui| {
            ui.label("📦 系统日志清理");
            ui.horizontal(|ui| {
                if ui.button("扫描系统日志").clicked() {
                    // 后续实现
                }
                ui.label("预计可清理: 计算中...");
            });
        });
    }

    /// 渲染设置页面
    fn render_settings_tab(&mut self, ui: &mut egui::Ui) {
        ui.heading("设置");
        ui.separator();

        ui.checkbox(&mut self.ui_state.show_hidden_files, "显示隐藏文件");

        ui.horizontal(|ui| {
            ui.label("最小文件大小过滤 (MB):");
            ui.add(egui::Slider::new(
                &mut self.ui_state.min_file_size_mb,
                0.0..=1000.0,
            ));
        });

        ui.horizontal(|ui| {
            ui.label("搜索关键词:");
            ui.text_edit_singleline(&mut self.ui_state.search_keyword);
        });
    }

    /// 显示文件夹选择对话框
    fn show_folder_dialog(&mut self) {
        if let Some(path) = rfd::FileDialog::new()
            .set_directory(&self.selected_path)
            .pick_folder()
        {
            self.selected_path = path.to_string_lossy().to_string();
            log::info!("选择了新的扫描路径: {}", self.selected_path);
        }
    }

    /// 检查扫描结果和进度
    fn check_scan_result(&mut self) {
        // 检查进度更新
        if let Some(progress_receiver) = &self.progress_receiver {
            while let Ok(progress) = progress_receiver.try_recv() {
                // 使用总体进度而不是阶段进度
                self.scan_progress = progress.total_progress;

                // 构建状态消息
                let phase_desc = match progress.phase {
                    crate::core::scanner::ScanPhase::Initializing => "正在初始化",
                    crate::core::scanner::ScanPhase::ScanningFiles => "正在扫描文件",
                    crate::core::scanner::ScanPhase::AnalyzingJunk => "正在分析垃圾文件",
                    crate::core::scanner::ScanPhase::FindingDuplicates => "正在查找重复文件",
                    crate::core::scanner::ScanPhase::AnalyzingLargeFiles => "正在分析大文件",
                    crate::core::scanner::ScanPhase::Completed => "扫描完成",
                };

                // 显示当前扫描路径
                if !progress.current_path.is_empty() && progress.current_path != "扫描完成" {
                    let display_path =
                        get_display_path(&std::path::Path::new(&progress.current_path), 40);
                    self.scan_status = phase_desc.to_string() + " - " + &display_path;
                } else {
                    self.scan_status = phase_desc.to_string();
                }
            }
        }

        // 检查扫描结果
        if let Some(receiver) = &self.scan_receiver {
            match receiver.try_recv() {
                Ok(result) => {
                    log::info!(
                        "收到扫描结果: {} 个文件, {} 个文件夹",
                        result.file_count,
                        result.folder_count
                    );
                    self.scan_result = Some(result);
                    self.is_scanning = false;
                    self.scan_progress = 1.0;
                    self.scan_status = "扫描完成".to_string();
                    self.scan_receiver = None;
                    self.progress_receiver = None;
                }
                Err(mpsc::TryRecvError::Empty) => {
                    // 正常情况，还没有结果
                }
                Err(mpsc::TryRecvError::Disconnected) => {
                    log::warn!("扫描线程意外断开连接");
                    self.is_scanning = false;
                    self.scan_status = "扫描失败".to_string();
                    self.scan_receiver = None;
                    self.progress_receiver = None;
                }
            }
        }
    }

    /// 渲染垃圾文件详情窗口
    fn render_junk_details_window(&mut self, ctx: &egui::Context) {
        egui::Window::new("🗑️ 垃圾文件详细分类")
            .default_width(800.0)
            .default_height(600.0)
            .resizable(true)
            .show(ctx, |ui| {
                if let Some(result) = &self.scan_result {
                    ui.horizontal(|ui| {
                        ui.heading("垃圾文件分类详情");
                        ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                            if ui.button("❌ 关闭").clicked() {
                                self.ui_state.show_junk_details = false;
                            }
                        });
                    });

                    ui.separator();

                    // 总体统计
                    ui.group(|ui| {
                        ui.horizontal(|ui| {
                            ui.label("📊 总计:");
                            ui.label(format!(
                                "垃圾文件数量: {}",
                                format_number(result.junk_file_count)
                            ));
                            ui.separator();
                            ui.label(format!(
                                "垃圾文件大小: {}",
                                format_size(result.junk_file_size)
                            ));
                            ui.separator();
                            let percentage = if result.total_size > 0 {
                                (result.junk_file_size as f64 / result.total_size as f64) * 100.0
                            } else {
                                0.0
                            };
                            ui.label(format!("占总空间: {:.1}%", percentage));
                        });
                    });

                    ui.add_space(10.0);

                    // 分类列表
                    egui::ScrollArea::vertical()
                        .max_height(450.0)
                        .show(ui, |ui| {
                            self.render_junk_category(
                                ui,
                                "🗂️ 系统临时文件",
                                "可以安全删除的系统临时文件和缓存",
                                result.junk_file_count / 4, // 模拟数据
                                result.junk_file_size / 4,
                            );

                            ui.add_space(5.0);

                            self.render_junk_category(
                                ui,
                                "🌐 浏览器缓存",
                                "浏览器存储的临时数据和缓存文件",
                                result.junk_file_count / 3,
                                result.junk_file_size / 3,
                            );

                            ui.add_space(5.0);

                            self.render_junk_category(
                                ui,
                                "📝 系统日志",
                                "系统和应用程序的日志文件",
                                result.junk_file_count / 6,
                                result.junk_file_size / 6,
                            );

                            ui.add_space(5.0);

                            self.render_junk_category(
                                ui,
                                "🖼️ 缩略图缓存",
                                "图片和视频的缩略图缓存文件",
                                result.junk_file_count / 8,
                                result.junk_file_size / 8,
                            );
                        });
                } else {
                    ui.vertical_centered(|ui| {
                        ui.add_space(100.0);
                        ui.label("暂无扫描数据");
                    });
                }
            });
    }

    /// 渲染垃圾文件分类项
    fn render_junk_category(
        &self,
        ui: &mut egui::Ui,
        title: &str,
        description: &str,
        count: u64,
        size: u64,
    ) {
        ui.group(|ui| {
            ui.vertical(|ui| {
                ui.horizontal(|ui| {
                    ui.label(egui::RichText::new(title).size(16.0).strong());
                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        if ui.small_button("📂 查看文件").clicked() {
                            // 后续实现查看具体文件列表
                        }
                        ui.label(format_size(size));
                        ui.separator();
                        ui.label(format!("{} 个文件", format_number(count)));
                    });
                });

                ui.horizontal(|ui| {
                    ui.add_space(10.0);
                    ui.label(
                        egui::RichText::new(description)
                            .size(12.0)
                            .color(egui::Color32::GRAY),
                    );
                });

                if count > 0 {
                    ui.horizontal(|ui| {
                        ui.add_space(10.0);
                        ui.add(
                            egui::ProgressBar::new(size as f32 / 1_000_000_000.0) // 假设最大1GB
                                .desired_width(200.0),
                        );

                        let percentage = if size > 0 {
                            (size as f64 / 1_000_000_000.0 * 100.0).min(100.0)
                        } else {
                            0.0
                        };
                        ui.label(format!("{:.1}%", percentage));
                    });
                }
            });
        });
    }
}

impl eframe::App for DiskCleanerApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // 应用炫酷主题
        self.theme_manager.apply_to_context(ctx);

        // 检查扫描结果
        self.check_scan_result();

        egui::CentralPanel::default().show(ctx, |ui| {
            // 渲染工具栏
            self.render_toolbar(ui);
            ui.separator();

            // 渲染标签页
            self.render_tabs(ui);

            // 根据当前标签页渲染对应内容
            match self.ui_state.current_tab {
                TabType::Analysis => self.render_analysis_tab(ui),
                TabType::Cleanup => self.render_cleanup_tab(ui),
                TabType::Settings => self.render_settings_tab(ui),
            }
        });

        // 渲染垃圾文件详情窗口
        if self.ui_state.show_junk_details {
            self.render_junk_details_window(ctx);
        }

        // 如果正在扫描，定期刷新界面
        if self.is_scanning {
            ctx.request_repaint();
        }
    }
}
