/// 扫描进度信息
#[derive(Debug, Clone)]
pub struct ScanProgress {
    /// 当前扫描的路径
    pub current_path: String,
    /// 已扫描的文件数
    pub files_scanned: u64,
    /// 已扫描的文件夹数
    pub folders_scanned: u64,
    /// 已发现的垃圾文件大小
    pub junk_size_found: u64,
    /// 已发现的垃圾文件数量
    pub junk_count_found: u64,
    /// 扫描阶段
    pub phase: ScanPhase,
    /// 当前阶段进度百分比 (0.0 - 1.0)
    pub phase_progress: f32,
    /// 总体进度百分比 (0.0 - 1.0)
    pub total_progress: f32,
}

/// 扫描阶段
#[derive(Debug, Clone, PartialEq)]
pub enum ScanPhase {
    /// 初始化
    Initializing,
    /// 扫描文件系统
    ScanningFiles,
    /// 分析垃圾文件
    AnalyzingJunk,
    /// 查找重复文件
    FindingDuplicates,
    /// 分析大文件
    AnalyzingLargeFiles,
    /// 完成
    Completed,
}

impl ScanPhase {
    /// 获取阶段描述
    pub fn description(&self) -> &'static str {
        match self {
            ScanPhase::Initializing => "正在初始化...",
            ScanPhase::ScanningFiles => "正在扫描文件系统...",
            ScanPhase::AnalyzingJunk => "正在分析垃圾文件...",
            ScanPhase::FindingDuplicates => "正在查找重复文件...",
            ScanPhase::AnalyzingLargeFiles => "正在分析大文件...",
            ScanPhase::Completed => "扫描完成",
        }
    }

    /// 获取阶段权重（用于计算总体进度）
    pub fn weight(&self) -> f32 {
        match self {
            ScanPhase::Initializing => 0.05,
            ScanPhase::ScanningFiles => 0.60,
            ScanPhase::AnalyzingJunk => 0.20,
            ScanPhase::FindingDuplicates => 0.10,
            ScanPhase::AnalyzingLargeFiles => 0.05,
            ScanPhase::Completed => 0.0,
        }
    }

    /// 获取阶段开始时的总体进度
    pub fn start_progress(&self) -> f32 {
        match self {
            ScanPhase::Initializing => 0.0,
            ScanPhase::ScanningFiles => 0.05,
            ScanPhase::AnalyzingJunk => 0.65,
            ScanPhase::FindingDuplicates => 0.85,
            ScanPhase::AnalyzingLargeFiles => 0.95,
            ScanPhase::Completed => 1.0,
        }
    }
}

/// 扫描进度管理器
pub struct ScanProgressManager {
    current_phase: ScanPhase,
    phase_progress: f32,
}

impl ScanProgressManager {
    /// 创建新的进度管理器
    pub fn new() -> Self {
        Self {
            current_phase: ScanPhase::Initializing,
            phase_progress: 0.0,
        }
    }

    /// 设置当前阶段
    pub fn set_phase(&mut self, phase: ScanPhase) {
        self.current_phase = phase;
        self.phase_progress = 0.0;
    }

    /// 更新阶段进度
    pub fn update_phase_progress(&mut self, progress: f32) {
        self.phase_progress = progress.clamp(0.0, 1.0);
    }

    /// 计算总体进度
    pub fn calculate_total_progress(&self) -> f32 {
        let base_progress = self.current_phase.start_progress();
        let phase_weight = self.current_phase.weight();
        let phase_contribution = phase_weight * self.phase_progress;
        
        (base_progress + phase_contribution).clamp(0.0, 1.0)
    }

    /// 创建进度信息
    pub fn create_progress(
        &self,
        current_path: String,
        files_scanned: u64,
        folders_scanned: u64,
        junk_size_found: u64,
        junk_count_found: u64,
    ) -> ScanProgress {
        ScanProgress {
            current_path,
            files_scanned,
            folders_scanned,
            junk_size_found,
            junk_count_found,
            phase: self.current_phase.clone(),
            phase_progress: self.phase_progress,
            total_progress: self.calculate_total_progress(),
        }
    }
}

impl Default for ScanProgressManager {
    fn default() -> Self {
        Self::new()
    }
}

impl ScanProgress {
    /// 格式化显示当前状态
    pub fn format_status(&self) -> String {
        match self.phase {
            ScanPhase::Initializing => "正在初始化扫描...".to_string(),
            ScanPhase::ScanningFiles => {
                format!("正在扫描: {} 个文件, {} 个文件夹", 
                    self.files_scanned, self.folders_scanned)
            },
            ScanPhase::AnalyzingJunk => {
                format!("已发现 {} 个垃圾文件 ({:.1} MB)", 
                    self.junk_count_found, 
                    self.junk_size_found as f64 / 1024.0 / 1024.0)
            },
            ScanPhase::FindingDuplicates => "正在查找重复文件...".to_string(),
            ScanPhase::AnalyzingLargeFiles => "正在分析大文件...".to_string(),
            ScanPhase::Completed => {
                format!("扫描完成: {} 个文件, {} 个垃圾文件", 
                    self.files_scanned, self.junk_count_found)
            },
        }
    }

    /// 获取当前扫描的文件名（用于显示）
    pub fn get_current_file_display(&self) -> String {
        if self.current_path.len() > 60 {
            format!("...{}", &self.current_path[self.current_path.len() - 57..])
        } else {
            self.current_path.clone()
        }
    }
}
